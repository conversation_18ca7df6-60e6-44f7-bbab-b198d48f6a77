"use strict";

const gulp = require("gulp");
const path = require("path");
const build = require("@microsoft/sp-build-web");
var getTasks = build.rig.getTasks;
build.rig.getTasks = function () {
	var result = getTasks.call(build.rig);

	result.set("serve", result.get("serve-deprecated"));

	return result;
};
build.configureWebpack.mergeConfig({
	additionalConfiguration: (generatedConfiguration) => {
		if (!generatedConfiguration.module.rules) {
			generatedConfiguration.module.rules = [];
		}
		generatedConfiguration.module.rules.push({
			test: /\.tailwind\.css$/i,
			use: [
				{
					loader: "postcss-loader",
					options: {
						postcssOptions: {
							config: path.resolve(__dirname, "postcss.tailwind.config.js"),
						},
					},
				},
			],
		});
		return generatedConfiguration;
	},
});

build.initialize(gulp);

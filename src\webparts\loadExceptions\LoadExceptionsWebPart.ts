import * as React from "react";
import * as ReactDom from "react-dom";
import { Version } from "@microsoft/sp-core-library";
import { type IPropertyPaneConfiguration } from "@microsoft/sp-property-pane";
import { BaseClientSideWebPart } from "@microsoft/sp-webpart-base";
import { PwCSPClient } from "@pwc-uk-risk-cdardigital/sp-client";

import * as strings from "LoadExceptionsWebPartStrings";
import LoadExceptions from "./components/LoadExceptions";
import { ILoadExceptionsProps } from "./components/ILoadExceptionsProps";

import "./index.tailwind.css";

export interface ILoadExceptionsWebPartProps {
    description: string;
}

export default class LoadExceptionsWebPart extends BaseClientSideWebPart<ILoadExceptionsWebPartProps> {
    public render(): void {
        const element: React.ReactElement<ILoadExceptionsProps> = React.createElement(LoadExceptions, {
            client: new PwCSPClient(this.context.spHttpClient, this.context.pageContext.web.serverRelativeUrl),
            context: this.context,
        });
        ReactDom.render(element, this.domElement);
    }

    protected onDispose(): void {
        ReactDom.unmountComponentAtNode(this.domElement);
    }

    protected get dataVersion(): Version {
        return Version.parse("1.0");
    }

    protected getPropertyPaneConfiguration(): IPropertyPaneConfiguration {
        return {
            pages: [
                {
                    header: {
                        description: strings.PropertyPaneDescription,
                    },
                    groups: [
                        {
                            groupName: strings.BasicGroupName,
                            groupFields: [],
                        },
                    ],
                },
            ],
        };
    }
}

import * as React from "react";
import type { ILoadExceptionsProps } from "./ILoadExceptionsProps";
import { ProgressIndicat<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Spinner } from "@fluentui/react";
import * as XLSX from "xlsx";
import { getModelData } from "./modelScript";
import {
    areExceptionEqual,
    getCloseDownExceptionRequests,
    getCreateExceptionRequests,
    getUpdateExceptionRequests,
    getResolveExceptionRequests,
} from "./spHelperFunctions";

function onBeforeExit(e: BeforeUnloadEvent) {
    e.preventDefault();
    e.returnValue = "Are you sure you want to leave? You will lose any incomplete changes.";
    return "Are you sure you want to leave? You will lose any incomplete changes.";
}
export default function LoadExceptions({ client }: ILoadExceptionsProps): JSX.Element {
    const [loading, setLoading] = React.useState(false);
    const [loadingExceptions, setLoadingExceptions] = React.useState(false);
    const [importComplete, setImportComplete] = React.useState(false);
    const inputRef = React.useRef<HTMLInputElement>(null);
    const [progressPercentage, setProgressPercentage] = React.useState<number>(0);
    const [progressDescription, setProgressDescription] = React.useState<string>(
        "Gathering existing and new exception data..."
    );
    const [progressLabel, setProgressLabel] = React.useState("Loading exceptions...");
    const [errorMessage, setErrorMessage] = React.useState("");
    const importExceptions = React.useCallback(async () => {
        setLoadingExceptions(true);
        setErrorMessage("");
        setProgressPercentage(0);

        const file = inputRef.current?.files?.[0];
        if (file) {
            try {
                window.addEventListener("beforeunload", onBeforeExit);
                await new Promise<void>((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = async (e) => {
                        try {
                            const data = e.target?.result;
                            if (data) {
                                setProgressPercentage(0.1);

                                const workbook = XLSX.read(data, { type: "array" });
                                const ModelExceptions: Array<{ [key: string]: string | number | null }> =
                                    (await getModelData(workbook)) || [];

                                const unreconciledModelExceptions = ModelExceptions.filter(
                                    (item) => item.Comments !== "Reconciles with PAD"
                                );
                                const AssigmentMappings = await client
                                    .list("Exceptions Assignment Mappings")
                                    .items.list();
                                const ExceptionTypes = await client.list("Exception Types").items.list();

                                const Exceptions = await client.list("Exceptions").items.list();

                                const UnreconciledSPExceptions = Exceptions.filter(
                                    (item) =>
                                        item.Exceptionstatus !== "Resolved - reconciled" &&
                                        item.Exceptionstatus !== "No further action - closed"
                                );

                                // get new model exceptions not in SP and create new exceptions
                                const NewModelExceptionsNotinSP = unreconciledModelExceptions.filter(
                                    (item) =>
                                        !UnreconciledSPExceptions.some((spItem) => spItem.PNR === item["PNR NUMBER"])
                                );
                                console.log("New Model Exceptions Not in SP", NewModelExceptionsNotinSP);
                                const createNewExceptionsBatch = getCreateExceptionRequests(
                                    client,
                                    NewModelExceptionsNotinSP,
                                    new Date(),
                                    AssigmentMappings,
                                    ExceptionTypes
                                );
                                // console.log("Batch to create model exceptions not in SP", createNewExceptionsBatch);
                                setProgressDescription(
                                    `Creating ${NewModelExceptionsNotinSP.length} new exceptions that were not in the EMT...`
                                );
                                setProgressPercentage(0.2);
                                await client.batchRequests(createNewExceptionsBatch);
                                // get sp exceptions that no longer have model exceptions and create resolce batch request

                                const openSPExceptionsWithNoModelData = UnreconciledSPExceptions.filter(
                                    (item) =>
                                        !unreconciledModelExceptions.some(
                                            (modelItem) => modelItem["PNR NUMBER"] === item.PNR
                                        )
                                );

                                const resolveExceptionRequest = getResolveExceptionRequests(
                                    client,
                                    openSPExceptionsWithNoModelData
                                );
                                // console.log("resolveExceptionRequest", resolveExceptionRequest);
                                setProgressDescription(
                                    `Resolving ${openSPExceptionsWithNoModelData.length} old exceptions that are no longer in the model output...`
                                );
                                setProgressPercentage(0.3);
                                await client.batchRequests(resolveExceptionRequest.payload);
                                await client.batchRequests(resolveExceptionRequest.actionPayload);
                                // get sharepoint exceptions that are resolved - no further action but have a model exception with new data
                                //create batch requests to close old and create new ones
                                const OpenSPwithModelData_NoFurtherAction = UnreconciledSPExceptions.filter(
                                    (item) =>
                                        unreconciledModelExceptions.some(
                                            (modelItem) => modelItem["PNR NUMBER"] === item.PNR
                                        ) && item.Exceptionstatus === "Resolved - no further action"
                                );
                                // console.log("OpenSPwithModelData_NoFurtherAction", OpenSPwithModelData_NoFurtherAction);

                                const OpenSPwithModelData_NoFurtherAction_newData =
                                    OpenSPwithModelData_NoFurtherAction.filter((item) => {
                                        const modelItem = unreconciledModelExceptions.find(
                                            (modelItem) => modelItem["PNR NUMBER"] === item.PNR
                                        ) as Record<string, unknown>;
                                        const newData = !areExceptionEqual(item, modelItem, ExceptionTypes);

                                        return newData;
                                    });

                                console.log(
                                    "no further action sp exceptions with model outputs that now have new data, we will be closing these one and then creating new ones",
                                    OpenSPwithModelData_NoFurtherAction_newData
                                );

                                const closeExceptionsBatch = getCloseDownExceptionRequests(
                                    client,
                                    OpenSPwithModelData_NoFurtherAction_newData
                                );
                                // console.log(
                                //     "closing exceptions that were no further action but have new data",
                                //     closeExceptionsBatch
                                // );
                                setProgressDescription(
                                    `Closing ${OpenSPwithModelData_NoFurtherAction_newData.length} 'No further action' exceptions. New data has been found and new exceptions will be created for these...`
                                );
                                setProgressPercentage(0.55);
                                await client.batchRequests(closeExceptionsBatch.payload);
                                await client.batchRequests(closeExceptionsBatch.actionPayload);
                                const NewModelExceptionsThatWereNFA = unreconciledModelExceptions.filter((item) =>
                                    OpenSPwithModelData_NoFurtherAction_newData.some(
                                        (spitem) => item["PNR NUMBER"] === spitem.PNR
                                    )
                                );
                                // console.log("NewModelExceptionsThatWereNFA", NewModelExceptionsThatWereNFA);

                                const createExceptionsBatch = getCreateExceptionRequests(
                                    client,
                                    NewModelExceptionsThatWereNFA,
                                    new Date(),
                                    AssigmentMappings,
                                    ExceptionTypes
                                );
                                // console.log("create new exceptions for the ones that were closed as new data found", createExceptionsBatch);
                                setProgressDescription(
                                    `Creating ${NewModelExceptionsThatWereNFA.length} new exceptions for the 'No further action' exceptions that had new data...`
                                );
                                setProgressPercentage(0.75);
                                await client.batchRequests(createExceptionsBatch);
                                // get sharepoint exceptions that are open and have model data but are not resolved - no further action. Create update batch request

                                const OpenSPwithModelData_NotNoFurtherAction = UnreconciledSPExceptions.filter(
                                    (item) =>
                                        unreconciledModelExceptions.some(
                                            (modelItem) => modelItem["PNR NUMBER"] === item.PNR
                                        ) && item.Exceptionstatus !== "Resolved - no further action"
                                );
                                console.log(
                                    "open sp exceptions that are not NFA, which we are updating to reflect model data",
                                    OpenSPwithModelData_NotNoFurtherAction
                                );
                                const modelExceptionsForUpdate = unreconciledModelExceptions.filter((item) =>
                                    OpenSPwithModelData_NotNoFurtherAction.some(
                                        (spitem) => item["PNR NUMBER"] === spitem.PNR
                                    )
                                );
                                // console.log("modelExceptionsForUpdate", modelExceptionsForUpdate);

                                const updateBatchRequest = getUpdateExceptionRequests(
                                    client,
                                    modelExceptionsForUpdate,
                                    OpenSPwithModelData_NotNoFurtherAction,
                                    AssigmentMappings,
                                    ExceptionTypes
                                );
                                // console.log("updateBatchRequest", updateBatchRequest);
                                setProgressDescription(
                                    `Updating ${modelExceptionsForUpdate.length} existing exceptions with the latest data...`
                                );
                                setProgressPercentage(0.8);
                                await client.batchRequests(updateBatchRequest);

                                //write update to Upload log

                                const payload = {
                                    "New exceptions created": NewModelExceptionsNotinSP.length,
                                    "Old exceptions resolved": openSPExceptionsWithNoModelData.length,
                                    "No further action exceptions with new data closed and new ones created":
                                        OpenSPwithModelData_NoFurtherAction_newData.length,
                                    "Existing exceptions updated": modelExceptionsForUpdate.length,
                                };
                                const resp = await client
                                    .list("Upload Log")
                                    .items.create({ Uploaddetails: JSON.stringify(payload) });

                                setProgressDescription("");
                                setProgressPercentage(1);
                                setProgressLabel("");
                                resolve();
                            }
                        } catch (error) {
                            reject(error);
                        }
                    };
                    reader.onerror = reject;
                    reader.readAsArrayBuffer(file);
                });
            } catch (error) {
                setErrorMessage(error.message);
                console.error("Error importing exceptions:", error);
            } finally {
                setLoadingExceptions(false);
                window.removeEventListener("beforeunload", onBeforeExit);
                setImportComplete(true);
            }
        }
    }, [client]);

    const saveFileToLibrary = React.useCallback(async () => {
        const file = inputRef.current?.files?.[0];
        if (file) {
            setLoading(true);
            try {
                const fileBuffer = await file.arrayBuffer();
                const fileName = file.name;
                await client.files.add({
                    content: fileBuffer,
                    fileName: fileName,
                    overwrite: true,
                    folderUrl: `/sites/ExceptionManagementToolDev/ExceptionManagementTool/Exception%20Reports`,
                });
                await client.files.add({
                    content: fileBuffer,
                    fileName: "NRS Latest.xlsx",
                    overwrite: true,
                    folderUrl: `/sites/ExceptionManagementToolDev/ExceptionManagementTool/Exception%20Reports`,
                });
                console.log("File saved to library");
            } catch (error) {
                console.error("Error saving file to library:", error);
            } finally {
                setLoading(false);
            }
        }
    }, [client, inputRef]);

    const uploadFile = React.useCallback(async () => {
        const inputEl = inputRef.current;
        if (!inputEl) {
            console.log("input not found");

            return;
        }

        await new Promise<void>((resolve) => {
            inputEl.onchange = () => {
                const file = inputRef.current?.files?.[0];
                if (file) {
                    try {
                        const reader = new FileReader();
                        reader.onload = async (e) => {
                            const data = e.target?.result;
                            const workbook = XLSX.read(data, { type: "array" });
                            const sheetNames = workbook.SheetNames;

                            const requiredTabs = [
                                "Loops to RPA Rec",
                                "RPA Issued Data",
                                "RPA 7253 Refunds",
                                "RPA 7033 Refunds",
                                "RPA MPM Data",
                                "MPM Coupon Refunds",
                                "MPM Ticket Refunds",
                                "Loops Data",
                            ];

                            const missingTabs = requiredTabs.filter((tab) => !sheetNames.includes(tab));

                            if (missingTabs.length > 0) {
                                setErrorMessage("File is missing the following tabs: " + missingTabs.join(", "));
                                console.error("File is missing the following tabs:", missingTabs);
                                return;
                            }
                        };
                        reader.readAsArrayBuffer(file);
                        saveFileToLibrary();
                    } catch (error) {
                        setErrorMessage("Error reading file: " + error.message);
                        console.error("Error reading file:", error);
                        return;
                    }
                }

                resolve();
            };
            inputEl.click();
        });
    }, [saveFileToLibrary]);

    const onUploadClick = React.useCallback(async () => {
        if (!loading) {
            await uploadFile();
        }
    }, [loading, uploadFile]);

    return (
        <div className="w-flex w-flex-col w-space-y-2">
            <input type="file" ref={inputRef} className="w-hidden" accept=".xlsx, .xls" />
            <span>Upload latest NRS outputs</span>
            {/* add description for telling them which file to upload */}
            <span>
                Please upload the latest NRS output. The file should be in Excel format and contain the latest
                exceptions data. The file should be named in the format "DD mmm YY Combined Output.xlsx", with todays
                date at the beginning.
            </span>
            <div className="w-flex w-flex-col w-space-y-2"></div>{" "}
            <DefaultButton
                disabled={loading || inputRef.current?.files !== undefined}
                onClick={onUploadClick}
                className="w-border-spacing-1"
            >
                Upload file
            </DefaultButton>
            {loading && <Spinner label="Uploading file..." />}
            {errorMessage && <span className="w-text-red-500">{errorMessage}</span>}
            {inputRef.current?.files?.[0] && !loading && !errorMessage && (
                <div className="w-flex">
                    <img
                        src="https://res-1.cdn.office.net/files/fabric-cdn-prod_20240610.001/assets/item-types/20/xlsx.svg"
                        alt="Excel Logo"
                        style={{ width: 24, height: 24, marginRight: 8 }}
                    />
                    <span>{inputRef.current.files[0].name}</span>
                </div>
            )}
            <PrimaryButton
                disabled={loading || loadingExceptions || inputRef.current?.files === undefined}
                onClick={importExceptions}
                className="w-border-spacing-1"
            >
                Import exceptions
            </PrimaryButton>
            {(loadingExceptions || importComplete) && (
                <ProgressIndicator
                    label={progressLabel}
                    description={progressDescription}
                    percentComplete={progressPercentage}
                />
            )}
            {importComplete && (
                <span>
                    Click to view the{" "}
                    <a href="https://virginatlantic365.sharepoint.com/sites/ExceptionManagementTool/Lists/Upload%20Log/AllItems.aspx">
                        upload log
                    </a>
                    {". "}
                    Please refresh the page to create a new import
                </span>
            )}
        </div>
    );
}

{"env": {"browser": true, "es2021": true}, "settings": {"react": {"version": "detect"}}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["@typescript-eslint", "react"], "rules": {"no-unused-vars": "off", "no-mixed-spaces-and-tabs": "off", "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_", "caughtErrorsIgnorePattern": "^_"}], "no-async-promise-executor": "off", "@typescript-eslint/no-var-requires": ["off"], "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/ban-types": ["warn", {"types": {"{}": false}}]}}
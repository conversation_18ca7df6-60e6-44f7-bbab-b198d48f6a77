import * as XLSX from "xlsx";

const getWorksheetData = (workbook: any, name: string) => {
    const book = workbook.Sheets[name];
    const range = XLSX.utils.decode_range(book["!ref"]);
    const numRows = range.e.r + 1;
    const values: Array<string | number | Date> = [];
    const chunkSize = 1000;
    const rangeColStart = range.s.c;
    const rangeColCount = range.e.c + 1;
    for (let i = 0; i <= numRows; i += chunkSize) {
        const chunkRows = i + chunkSize > numRows ? numRows - i : chunkSize;
        const chunkRange = XLSX.utils.encode_range({
            s: { r: i, c: rangeColStart },
            e: { r: i + chunkRows - 1, c: rangeColStart + rangeColCount - 1 },
        });
        const chunkValues = XLSX.utils.sheet_to_json(book, { range: chunkRange, header: 1 });
        values.push(...(chunkValues as Array<string | number | Date>));
    }
    return values;
};

function excelSerialDateToJSDate(serial: number): Date {
    // Number of days between Excel's epoch (Jan 1, 1900) and Unix epoch (Jan 1, 1970)
    const excelEpochToUnixEpochDays = 25569;
    const millisecondsPerDay = 86400000; // 24 * 60 * 60 * 1000

    // Adjust for Excel's leap year bug for dates before March 1, 1900
    if (serial < 60) {
        serial--;
    }

    // Calculate the number of milliseconds since Unix epoch
    const utcDays = serial - excelEpochToUnixEpochDays;
    const utcMillis = utcDays * millisecondsPerDay;

    // Return the JavaScript Date object
    return new Date(utcMillis);
}

function GetTotalGBP(workbookValues: (string | number | boolean)[][], columnName: string, pnr: string) {
    if (workbookValues) {
        const gbpCol = workbookValues[0].indexOf(`${columnName}`);
        const PNRCol = workbookValues[0].indexOf(`PNR NUMBER`);
        const GBPVals = workbookValues.filter((value) => value[PNRCol] === pnr);
        let GBPSum: number | null = null;
        if (GBPVals.length > 0) {
            GBPSum = 0;
            GBPVals.forEach((row) => {
                if (row[gbpCol]) {
                    GBPSum = (GBPSum as number) + (row[gbpCol] as number);
                }
            });
        }
        return Math.round((GBPSum ? 0 : (GBPSum as number)) * 100) / 100;
    } else {
        return null;
    }
}

function verifyData(workbook: any, workbookTitles: string[], data: Array<Array<string | number | any>>) {
    const errors: Array<string> = [];
    for (let i = 0; i < workbookTitles.length; i++) {
        const values: Array<string | number | any> = data[i];
        const headers = values[0];
        const pnrCol = headers.indexOf("PNR NUMBER");
        let pnrSet = new Set();
        for (let i = 1; i < values.length; i++) {
            const row = values[i];
            if (row[pnrCol] === null) {
                errors.push(`${workbookTitles[i]}, row ${i + 1} has a missing PNR NUMBER`);
            }
            const pnr = row[pnrCol] as string;
            if (pnrSet.has(pnr)) {
                errors.push(`${workbookTitles[i]} has duplicate PNR NUMBER found: ${pnr}`);
            } else {
                pnrSet.add(pnr);
            }
        }
    }
    return errors;
}

async function getModelData(workbook: any, workbookTitle?: string) {
    const values: Array<string | number | any> = getWorksheetData(workbook, workbookTitle ?? "Loops to RPA Rec");
    const RPAissuedData: Array<string | number | any> = getWorksheetData(workbook, "RPA Issued Data");
    const RefundData7253: Array<string | number | any> = getWorksheetData(workbook, "RPA 7253 Refunds");
    const RefundData7033: Array<string | number | any> = getWorksheetData(workbook, "RPA 7033 Refunds");
    const MPMData: Array<string | number | any> = getWorksheetData(workbook, "RPA MPM Data");
    const MPMCouponRefundData: Array<string | number | any> = getWorksheetData(workbook, "MPM Coupon Refunds");
    const MPMTicketRefundData: Array<string | number | any> = getWorksheetData(workbook, "MPM Ticket Refunds");
    const pnrFlightDates: { [pnr: string]: string } = {};

    const LoopsData: Array<string | number | any> = getWorksheetData(workbook, "Loops Data");
    const errors = verifyData(workbook, ["Loops to RPA Rec"], [values]);
    if (errors.length > 0) {
        throw new Error(`Workbook verification failed: ${errors.join(", ")}`);
    }

    const LoopsPNRCol = LoopsData[0].indexOf("PNR NUMBER");
    const LoopsActivityDateCol = LoopsData[0].indexOf("ActivityDate");

    const pnrCol = RPAissuedData[0].indexOf("PNR NUMBER");
    const flightDateCol = RPAissuedData[0].indexOf("FLIGHT DATE");
    for (let i = 1; i < RPAissuedData.length; i++) {
        const pnr = `${RPAissuedData[i][pnrCol]}`;
        const flightDate = RPAissuedData[i][flightDateCol] as number;
        const dateVal = excelSerialDateToJSDate(flightDate);
        //gets the earliest flight date for each PNR number
        if (pnr && flightDate) {
            const existingFlightDate = pnrFlightDates[pnr];
            if (existingFlightDate) {
                const currentDate = new Date(existingFlightDate);
                if (dateVal.getTime() < currentDate.getTime()) {
                    pnrFlightDates[pnr] = dateVal.toISOString();
                }
            } else {
                pnrFlightDates[pnr] = dateVal.toISOString();
            }
        }
    }
    for (let i = 1; i < LoopsData.length; i++) {
        const pnr = LoopsData[i][LoopsPNRCol] as string;
        const flightDate = LoopsData[i][LoopsActivityDateCol];
        const dateVal = new Date((flightDate as string).split(" ")[0]);
        //gets the earliest flight date for each PNR number
        if (pnr && flightDate) {
            const existingFlightDate = pnrFlightDates[pnr];
            if (existingFlightDate) {
                const currentDate = new Date(existingFlightDate);
                if (dateVal.getTime() < currentDate.getTime()) {
                    pnrFlightDates[pnr] = dateVal.toISOString();
                }
            } else {
                pnrFlightDates[pnr] = dateVal.toISOString();
            }
        }
    }
    const payload: Array<{ [key: string]: string | number | null }> = [];
    // start from 1 to remove headers -- add back in (values.length)
    for (let i = 1; i < values.length; i++) {
        const row: { [key: string]: string | number | null } = {};
        for (let j = 0; j < values[i].length; j++) {
            const value = values[i][j] ?? null;
            const header = `${values[0][j]}`;
            row[header] = typeof value === "string" && !value ? null : value;
        }
        if (row["PNR NUMBER"] && row["Bucket"] === "Unreconciled") {
            const flightDate = pnrFlightDates[row["PNR NUMBER"]];
            if (flightDate) {
                row["FLIGHT DATE"] = flightDate;
            }
            //rpa issued GBP data
            const GBPIssued7253: number | null = GetTotalGBP(
                RPAissuedData,
                "MILES AMOUNT (7253) GBP",
                row["PNR NUMBER"] as string
            );

            row["7253_MA_GBP"] = GBPIssued7253;

            const GBPIssued7033: number | null = GetTotalGBP(
                RPAissuedData,
                "MILES AMOUNT (7033) GBP",
                row["PNR NUMBER"] as string
            );

            row["7033_MA_GBP"] = GBPIssued7033;

            // 7253 refund data
            const GBPRefunded7523: number | null = GetTotalGBP(
                RefundData7253,
                "MILES AMOUNT (7253) GBP",
                row["PNR NUMBER"] as string
            );

            row["7253_Refunded_MA_GBP"] = GBPRefunded7523;

            // 7033 refund data
            const GBPRefunded7033: number | null = GetTotalGBP(
                RefundData7033,
                "MILES AMOUNT (7033) GBP",
                row["PNR NUMBER"] as string
            );

            row["7033_Refunded_MA_GBP"] = GBPRefunded7033;

            //MPM issued data
            const GBPIssuedMPM: number | null = GetTotalGBP(
                MPMData,
                "MILES AMOUNT (7285) GBP",
                row["PNR NUMBER"] as string
            );

            row["MPM_MA_GBP"] = GBPIssuedMPM;

            //MPM ticket refund data
            const GBPTicketRefundedMPM: number | null = GetTotalGBP(
                MPMTicketRefundData,
                "MILES AMOUNT (7285) GBP",
                row["PNR NUMBER"] as string
            );

            row["MPM_Ticket_Refunded_MA_GBP"] = GBPTicketRefundedMPM;

            //MPM Coupon refund data
            const GBPCouponRefundedMPM: number | null = GetTotalGBP(
                MPMCouponRefundData,
                "MILES AMOUNT (7285) GBP",
                row["PNR NUMBER"] as string
            );

            row["MPM_Coupon_Refunded_MA_GBP"] = GBPCouponRefundedMPM;
            row["Abs_Difference"] = Math.abs(row["Difference"] as number);

            payload.push(row);
        }
    }
    return payload;
}
export { getWorksheetData, getModelData };

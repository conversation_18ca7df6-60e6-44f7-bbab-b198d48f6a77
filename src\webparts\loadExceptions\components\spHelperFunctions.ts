const getCreateExceptionRequests = (
    client: any,
    modelExceptions: Array<{ [key: string]: string | number | null }>,
    createdtime: Date,
    mappings: Record<string, unknown>[],
    types: Record<string, unknown>[]
) => {
    const currentTime = new Date();
    const payload: {
        url: string;
        method: string;
        headers: { "If-Match": string; "X-HTTP-Method": string };
        body: string;
    }[] = [];
    modelExceptions.forEach((exception) => {
        const hoursDifference = Math.abs(currentTime.getTime() - createdtime.getTime()) / (1000 * 60 * 60);
        const exceptionType = types.find((item) => item.Title === exception.Comments);

        const mapping = mappings.find((item) => item.ExceptiontypeId === exceptionType?.ID);

        const exceptionStatus =
            exception?.Comments === "No RPA Entry" &&
            (hoursDifference < 72 || ((exception.Difference as number) > 0 && hoursDifference < 192))
                ? "Pending assignment"
                : "Assigned";

        const AssignedToId = exceptionStatus === "Assigned" ? mapping?.AssigntoId ?? null : null;

        const bipsValue = (exception["LOOPS POINTS SPENT"] as number) * 0.0028;

        payload.push({
            ...client.urls.list("Exceptions").items.create({
                ExceptionID: `${exception["PNR NUMBER"]}-${currentTime
                    .toISOString()
                    .replace(/[-:.TZ]/g, "")
                    .replace(/T/g, "")}`,
                Title: `${exception["PNR NUMBER"]}-${currentTime
                    .toISOString()
                    .replace(/[-:.TZ]/g, "")
                    .replace(/T/g, "")}`,
                PNR: `${exception["PNR NUMBER"]}`,
                Modelbucket: exception.Bucket,
                ExceptiontypeId: exceptionType?.ID,
                AssignedtoId: AssignedToId,
                Exceptionstatus: exceptionStatus,
                RPApointsrefunded: exception["RPA POINTS REFUNDED"],
                RPA7253value: exception["7253_MA_GBP"],
                EMDpointsmovement: exception["EMD ISSUED POINTS"],
                Difference: exception.Difference,
                BIPSvalue: bipsValue,
                Loopspointsspent: exception["LOOPS POINTS SPENT"],
                RPApointsmovement: exception["RPA ISSUED POINTS"],
                Flightdate: exception["FLIGHT DATE"],
                EMDpointsrefunded: exception["EMD REFUNDED POINTS"],
                MPMPointsMovement: exception["MPM ISSUED POINTS"],
                MPMPointsRefunded: exception["MPM REFUNDED POINTS"],
                AbsoluteDifference: exception["Abs_Difference"],
                OData__x0037_033IssuedGBP: exception["7033_MA_GBP"],
                OData__x0037_253RefundedGBP: exception["7253_Refunded_MA_GBP"],
                OData__x0037_033RefundedGBP: exception["7033_Refunded_MA_GBP"],
                MPMIssuedGBP: exception["MPM_MA_GBP"],
                MPMTicketRefundGBP: exception["MPM_Ticket_Refunded_MA_GBP"],
                MPMCouponRefundGBP: exception["MPM_Coupon_Refunded_MA_GBP"],
            }),
        });
    });
    console.log("data",payload)
    return payload;
};
const getUpdateExceptionRequests = (
    client: any,
    modelExceptions: Record<string, unknown>[],
    SPExceptions: Record<string, unknown>[],
    mappings: Record<string, unknown>[],
    types: Record<string, unknown>[]
) => {
    const payload: {
        url: string;
        method: string;
        headers: { "If-Match": string; "X-HTTP-Method": string };
        body: string;
    }[] = [];
    modelExceptions.forEach((exception) => {
        const SPException = SPExceptions.find((item) => item.PNR === exception["PNR NUMBER"]);

        const createdtime = new Date(SPException?.Created as string);
        const currentTime = new Date();
        const hoursDifference = Math.abs(currentTime.getTime() - createdtime.getTime()) / (1000 * 60 * 60);
        const exceptionType = types.find((item) => item.Title === exception.Comments);
        const mapping = mappings.find((item) => item.ExceptiontypeId === exceptionType?.ID);
        const currentStatus = SPException?.Exceptionstatus as string;
        const exceptionStatus =
            currentStatus !== "Pending assignment"
                ? currentStatus
                : exception?.Comments === "No RPA Entry" &&
                  (hoursDifference < 72 || ((exception.Difference as number) > 0 && hoursDifference < 192))
                ? "Pending assignment"
                : "Assigned";
        const AssignedToId =
            exceptionStatus === "Assigned" ? (!mapping?.AssigntoId ? null : mapping?.AssigntoId) : null;
        const bipsValue = (exception["LOOPS POINTS SPENT"] as number) * 0.0028;

        payload.push({
            url: `/sites/ExceptionManagementToolDev/ExceptionManagementToolDev/ExceptionManagementTool/_api/web/lists/getbytitle('Exceptions')/items(${SPException?.ID})`,
            method: "PATCH",
            headers: {
                "If-Match": "*",
                "X-HTTP-Method": "MERGE",
            },
            body: JSON.stringify({
                PNR: `${exception["PNR NUMBER"]}`,
                Modelbucket: exception.Bucket,
                ExceptiontypeId: exceptionType?.ID,
                AssignedtoId: AssignedToId,
                Exceptionstatus: exceptionStatus,
                RPApointsrefunded: exception["RPA POINTS REFUNDED"],
                RPA7253value: exception["7253_MA_GBP"],
                EMDpointsmovement: exception["EMD ISSUED POINTS"],
                Difference: exception.Difference,
                BIPSvalue: bipsValue,
                Loopspointsspent: exception["LOOPS POINTS SPENT"],
                RPApointsmovement: exception["RPA ISSUED POINTS"],
                Flightdate: exception["FLIGHT DATE"],
                EMDpointsrefunded: exception["EMD REFUNDED POINTS"],
                MPMPointsMovement: exception["MPM ISSUED POINTS"],
                MPMPointsRefunded: exception["MPM REFUNDED POINTS"],
                AbsoluteDifference: exception["Abs_Difference"],
                OData__x0037_033IssuedGBP: exception["7033_MA_GBP"],
                OData__x0037_253RefundedGBP: exception["7253_Refunded_MA_GBP"],
                OData__x0037_033RefundedGBP: exception["7033_Refunded_MA_GBP"],
                MPMIssuedGBP: exception["MPM_MA_GBP"],
                MPMTicketRefundGBP: exception["MPM_Ticket_Refunded_MA_GBP"],
                MPMCouponRefundGBP: exception["MPM_Coupon_Refunded_MA_GBP"],
            }),
        });
    });
    console.log("data",payload)
    return payload;
};
const getCloseDownExceptionRequests = (client: any, SPExceptions: Record<string, unknown>[]) => {
    const payload: {
        url: string;
        method: string;
        headers: { "If-Match": string; "X-HTTP-Method": string };
        body: string;
    }[] = [];
    const actionPayload: {
        url: string;
        method: string;
        headers: { "If-Match": string; "X-HTTP-Method": string };
        body: string;
    }[] = [];
    SPExceptions.forEach((exception) => {
        payload.push({
            url: `/sites/ExceptionManagementTool/_api/web/lists/getbytitle('Exceptions')/items(${exception?.ID})`,
            method: "PATCH",
            headers: {
                "If-Match": "*",
                "X-HTTP-Method": "MERGE",
            },
            body: JSON.stringify({
                Exceptionstatus: "No further action - closed",
            }),
        });

        actionPayload.push({
            ...client.urls.list("Exception Actions").items.create({
                ExceptionId: exception.ID,
                Actionpayload: JSON.stringify({
                    outcome: "No further action - closed",
                    reason: "New exception created",
                    comment: "Auto-closed due to new data for this PNR and new exception created",
                }),
                Actiontext: "Closed by model",
                Title: "Closed by model",
                Actiondate: new Date().toISOString(),
                ActionID: `${exception.ExceptionID}-closed-${new Date().toISOString()}`,
                ActionauthorId: 69, // this is the id of the service account
                Actiontype: "Closed",
            }),
        });
    });
    console.log("data",payload,actionPayload)
    return { payload, actionPayload };
};
const getResolveExceptionRequests = (client: any, SPExceptions: Record<string, unknown>[]) => {
    const payload: {
        url: string;
        method: string;
        headers: { "If-Match": string; "X-HTTP-Method": string };
        body: string;
    }[] = [];
    const actionPayload: {
        url: string;
        method: string;
        headers: { "If-Match": string; "X-HTTP-Method": string };
        body: string;
    }[] = [];
    SPExceptions.forEach((exception) => {
        payload.push({
            url: `/sites/ExceptionManagementToolDev/ExceptionManagementTool/_api/web/lists/getbytitle('Exceptions')/items(${exception?.ID})`,
            method: "PATCH",
            headers: {
                "If-Match": "*",
                "X-HTTP-Method": "MERGE",
            },
            body: JSON.stringify({
                Exceptionstatus: "Resolved - reconciled",
                // ResolvedById: 69, // this is the id of the service account
            }),
        });

        actionPayload.push({
            ...client.urls.list("Exception Actions").items.create({
                ExceptionId: exception.ID,
                Actionpayload: JSON.stringify({
                    outcome: "Resolved - reconciled",
                    reason: "Resolved in model",
                    comment: "Auto-reconciled in subsequent model run",
                }),
                Actiontext: "Resolved in model",
                Title: "Resolved in model",
                Actiondate: new Date().toISOString(),
                ActionID: `${exception.ExceptionID}-resolution-${new Date().toISOString()}`,
                ActionauthorId: 69, // this is the id of the service account
                Actiontype: "Resolution",
            }),
        });
    });
    console.log("data",payload,actionPayload)
    return { payload, actionPayload };
};

const areExceptionEqual = (
    SPException: Record<string, unknown>,
    modelException: Record<string, unknown>,
    exceptionTypes: Record<string, unknown>[]
) => {
    return (
        SPException.PNR === modelException["PNR NUMBER"] &&
        SPException.RPApointsmovement === modelException["RPA ISSUED POINTS"] &&
        SPException.RPApointsrefunded === modelException["RPA POINTS REFUNDED"] &&
        SPException.EMDpointsmovement === modelException["EMD ISSUED POINTS"] &&
        SPException.EMDpointsrefunded === modelException["EMD REFUNDED POINTS"] &&
        SPException.MPMPointsMovement === modelException["MPM ISSUED POINTS"] &&
        SPException.MPMPointsRefunded === modelException["MPM REFUND POINTS"] &&
        SPException.Loopspointsspent === modelException["LOOPS POINTS SPENT"] &&
        SPException.Difference === modelException.Difference &&
        SPException.Modelbucket === modelException.Bucket &&
        exceptionTypes.find((item) => item.ID === SPException.ExceptiontypeId)?.Title === modelException.Comments &&
        ((SPException.Flightdate ?? "") as string).split("T")[0] ===
            ((modelException["FLIGHT DATE"] ?? "") as string).split("T")[0] &&
        SPException.RPA7253value === modelException["7253_MA_GBP"] &&
        SPException.OData__x0037_033IssuedGBP === modelException["7033_MA_GBP"] &&
        SPException.OData__x0037_253RefundedGBP === modelException["7253_Refunded_MA_GBP"] &&
        SPException.OData__x0037_033RefundedGBP === modelException["7033_Refunded_MA_GBP"] &&
        SPException.MPMIssuedGBP === modelException["MPM_MA_GBP"] &&
        SPException.MPMTicketRefundGBP === modelException["MPM_Ticket_Refunded_MA_GBP"] &&
        SPException.MPMCouponRefundGBP === modelException["MPM_Coupon_Refunded_MA_GBP"] &&
        SPException.AbsoluteDifference === modelException["Abs_Difference"]
    );
};

export {
    getCreateExceptionRequests,
    getUpdateExceptionRequests,
    areExceptionEqual,
    getCloseDownExceptionRequests,
    getResolveExceptionRequests,
};

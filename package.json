{"name": "exceptions-load", "version": "0.0.1", "private": true, "engines": {"node": ">=16.13.0 <17.0.0 || >=18.17.1 <19.0.0"}, "main": "lib/index.js", "scripts": {"build": "gulp bundle", "clean": "gulp clean", "test": "gulp test", "serve": "gulp serve --nobrowser"}, "dependencies": {"@fluentui/react": "^8.106.4", "@microsoft/office-js": "^1.1.97", "@microsoft/sp-component-base": "1.18.2", "@microsoft/sp-core-library": "1.18.2", "@microsoft/sp-lodash-subset": "1.18.2", "@microsoft/sp-office-ui-fabric-core": "1.18.2", "@microsoft/sp-property-pane": "1.18.2", "@microsoft/sp-webpart-base": "1.18.2", "@pwc-uk-risk-cdardigital/sp-client": "file:pwc-uk-risk-cdardigital-sp-client-1.0.51.tgz", "@uifabric/file-type-icons": "^7.10.11", "react": "17.0.1", "react-dom": "17.0.1", "tslib": "2.3.1", "xlsx": "^0.18.5"}, "devDependencies": {"@microsoft/eslint-config-spfx": "1.18.2", "@microsoft/eslint-plugin-spfx": "1.18.2", "@microsoft/rush-stack-compiler-4.7": "0.1.0", "@microsoft/sp-build-web": "1.18.2", "@microsoft/sp-module-interfaces": "1.18.2", "@rushstack/eslint-config": "2.5.1", "@types/node": "^22.10.1", "@types/office-js": "^1.0.451", "@types/react": "17.0.45", "@types/react-dom": "17.0.17", "@types/webpack-env": "~1.15.2", "ajv": "^6.12.5", "autoprefixer": "^10.4.20", "eslint": "8.7.0", "eslint-plugin-react-hooks": "4.3.0", "gulp": "^4.0.2", "postcss": "^8.4.27", "postcss-loader": "^4.3.0", "tailwindcss": "^3.3.3", "typescript": "4.7"}}
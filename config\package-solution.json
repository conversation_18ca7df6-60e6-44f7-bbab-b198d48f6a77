{"$schema": "https://developer.microsoft.com/json-schemas/spfx-build/package-solution.schema.json", "solution": {"name": "exceptions-load-client-side-solution", "id": "d97a1fbe-05a3-4f64-9954-40b7da412905", "version": "*******", "includeClientSideAssets": true, "skipFeatureDeployment": true, "isDomainIsolated": false, "developer": {"name": "", "websiteUrl": "", "privacyUrl": "", "termsOfUseUrl": "", "mpnId": "Undefined-1.18.2"}, "metadata": {"shortDescription": {"default": "exceptions-load description"}, "longDescription": {"default": "exceptions-load description"}, "screenshotPaths": [], "videoUrl": "", "categories": []}, "features": [{"title": "exceptions-load Feature", "description": "The feature that activates elements of the exceptions-load solution.", "id": "378a4450-8b83-427b-b87a-8f281ff4fdd1", "version": "*******"}]}, "paths": {"zippedPackage": "solution/exceptions-load.sppkg"}}